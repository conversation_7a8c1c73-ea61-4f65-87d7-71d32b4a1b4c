# Minimal CMake Template for Qt 6 Projects

*This project is updated for Qt 6. Visit [qt5](https://github.com/euler0/mini-cmake-qt/tree/qt5) branch if you are looking for the Qt 5 template.*

This is a minimal, but complete and production-ready CMake project template for Qt 6 projects. This may be a starting point for your new application. If that is not intent to be a cross-platform project, you can clean up that stuffs from the template.

## Features
  * Handling all possible Qt stuffs (UI, resources, ~~translations~~)
  * macOS bundle
  * Windows executable icon

## macOS Deployment

If you want to deploy your application on macOS, I would recommend you read below documents.

* [Qt for macOS](https://doc.qt.io/qt-6/macos.html)
  * [Qt for macOS - Deployment](https://doc.qt.io/qt-6/macos-deployment.html)
  * [Qt for macOS - Specific Issues](https://doc.qt.io/qt-6/macos-issues.html)

## Windows Deployment

If you want to deploy your application on Windows, I would recommend you read below documents.

* [Qt for Winodws](https://doc.qt.io/qt-6/windows.html)
  * [Qt for Windows - Deployment](https://doc.qt.io/qt-6/windows-deployment.html)
  * [Qt for Windows - Specific Issues](https://doc.qt.io/qt-6/windows-issues.html)

## References
  * Building with CMake: https://doc.qt.io/qt-6/cmake-manual.html
  * Qt and CMake: The Past, the Present and the Future: https://www.qt.io/blog/qt-and-cmake-the-past-the-present-and-the-future
  * Using CMake with Qt 5: https://www.kdab.com/using-cmake-with-qt-5/
