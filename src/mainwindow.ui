<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>640</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Main Window</string>
  </property>
  <widget class="QWidget" name="centralwidget"/>
 </widget>
 <resources>
  <include location="../example.qrc"/>
 </resources>
 <connections/>
</ui>
